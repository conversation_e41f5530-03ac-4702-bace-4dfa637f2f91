{"version": 3, "sources": ["../../../../src/core/utils/internal/toReadonlyArray.ts"], "sourcesContent": ["/**\n * Creates an immutable copy of the given array.\n */\nexport function toReadonlyArray<T>(source: Array<T>): ReadonlyArray<T> {\n  const clone = [...source] as Array<T>\n  Object.freeze(clone)\n  return clone\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,gBAAmB,QAAoC;AACrE,QAAM,QAAQ,CAAC,GAAG,MAAM;AACxB,SAAO,OAAO,KAAK;AACnB,SAAO;AACT;", "names": []}