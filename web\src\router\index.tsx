import { createBrowserRouter, Navigate } from 'react-router-dom';

// Layouts e componentes de proteção
import { ProtectedRoute } from '@/components/auth/protected-route';
import { PublicRoute } from '@/components/auth/public-route';

// Páginas de autenticação
import { LoginPage } from '@/pages/auth/login';
import { SetPasswordPage } from '@/pages/auth/set-password';
import { TwoFactorPage } from '@/pages/auth/two-factor';

// Páginas protegidas
import { DashboardPage } from '@/pages/dashboard';

export const router = createBrowserRouter([
  // Rota raiz - redireciona para dashboard se autenticado, senão para login
  {
    path: '/',
    element: <Navigate to="/dashboard" replace />,
  },

  // Rotas públicas (apenas para usuários não autenticados)
  {
    path: '/login',
    element: (
      <PublicRoute>
        <LoginPage />
      </PublicRoute>
    ),
  },
  {
    path: '/auth/set-password',
    element: (
      <PublicRoute>
        <SetPasswordPage />
      </PublicRoute>
    ),
  },
  {
    path: '/auth/2fa',
    element: (
      <PublicRoute>
        <TwoFactorPage />
      </PublicRoute>
    ),
  },

  // Rotas protegidas (apenas para usuários autenticados)
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <DashboardPage />
      </ProtectedRoute>
    ),
  },

  // Rota 404 - página não encontrada
  {
    path: '*',
    element: (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold">404</h1>
          <p className="text-muted-foreground">Página não encontrada</p>
          <a
            href="/"
            className="mt-4 inline-block text-primary underline-offset-4 hover:underline"
          >
            Voltar ao início
          </a>
        </div>
      </div>
    ),
  },
]);
