{"version": 3, "sources": ["../../../../src/core/utils/internal/Disposable.ts"], "sourcesContent": ["export type DisposableSubscription = () => void\n\nexport class Disposable {\n  protected subscriptions: Array<DisposableSubscription> = []\n\n  public dispose() {\n    let subscription: DisposableSubscription | undefined\n    while ((subscription = this.subscriptions.shift())) {\n      subscription()\n    }\n  }\n}\n"], "mappings": "AAEO,MAAM,WAAW;AAAA,EACZ,gBAA+C,CAAC;AAAA,EAEnD,UAAU;AACf,QAAI;AACJ,WAAQ,eAAe,KAAK,cAAc,MAAM,GAAI;AAClD,mBAAa;AAAA,IACf;AAAA,EACF;AACF;", "names": []}