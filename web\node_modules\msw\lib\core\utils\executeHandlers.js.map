{"version": 3, "sources": ["../../../src/core/utils/executeHandlers.ts"], "sourcesContent": ["import {\n  RequestHandler,\n  type RequestHandlerExecutionResult,\n} from '../handlers/RequestHandler'\n\nexport interface HandlersExecutionResult {\n  handler: RequestHandler\n  parsedResult?: any\n  response?: Response\n}\n\nexport interface ResponseResolutionContext {\n  baseUrl?: string\n}\n\n/**\n * Executes the list of request handlers against the given request.\n * Returns the execution result object containing any matching request\n * handler and any mocked response it returned.\n */\nexport const executeHandlers = async <Handlers extends Array<RequestHandler>>({\n  request,\n  requestId,\n  handlers,\n  resolutionContext,\n}: {\n  request: Request\n  requestId: string\n  handlers: Handlers\n  resolutionContext?: ResponseResolutionContext\n}): Promise<HandlersExecutionResult | null> => {\n  let matchingHandler: RequestHandler | null = null\n  let result: RequestHandlerExecutionResult<any> | null = null\n\n  for (const handler of handlers) {\n    result = await handler.run({ request, requestId, resolutionContext })\n\n    // If the handler produces some result for this request,\n    // it automatically becomes matching.\n    if (result !== null) {\n      matchingHandler = handler\n    }\n\n    // Stop the lookup if this handler returns a mocked response.\n    // If it doesn't, it will still be considered the last matching\n    // handler until any of them returns a response. This way we can\n    // distinguish between fallthrough handlers without responses\n    // and the lack of a matching handler.\n    if (result?.response) {\n      break\n    }\n  }\n\n  if (matchingHandler) {\n    return {\n      handler: matchingHandler,\n      parsedResult: result?.parsedResult,\n      response: result?.response,\n    }\n  }\n\n  return null\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBO,MAAM,kBAAkB,OAA+C;AAAA,EAC5E;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAK+C;AAC7C,MAAI,kBAAyC;AAC7C,MAAI,SAAoD;AAExD,aAAW,WAAW,UAAU;AAC9B,aAAS,MAAM,QAAQ,IAAI,EAAE,SAAS,WAAW,kBAAkB,CAAC;AAIpE,QAAI,WAAW,MAAM;AACnB,wBAAkB;AAAA,IACpB;AAOA,QAAI,QAAQ,UAAU;AACpB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,SAAS;AAAA,MACT,cAAc,QAAQ;AAAA,MACtB,UAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}