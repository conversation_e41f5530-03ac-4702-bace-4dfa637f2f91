{"version": 3, "sources": ["../../../../src/core/utils/internal/checkGlobals.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\nimport { devUtils } from './devUtils'\n\nexport function checkGlobals() {\n  /**\n   * MSW expects the \"URL\" constructor to be defined.\n   * It's not present in React Native so suggest a polyfill\n   * instead of failing silently.\n   * @see https://github.com/mswjs/msw/issues/1408\n   */\n  invariant(\n    typeof URL !== 'undefined',\n    devUtils.formatMessage(\n      `Global \"URL\" class is not defined. This likely means that you're running MSW in an environment that doesn't support all Node.js standard API (e.g. React Native). If that's the case, please use an appropriate polyfill for the \"URL\" class, like \"react-native-url-polyfill\".`,\n    ),\n  )\n}\n"], "mappings": "AAAA,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AAElB,SAAS,eAAe;AAO7B;AAAA,IACE,OAAO,QAAQ;AAAA,IACf,SAAS;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;", "names": []}