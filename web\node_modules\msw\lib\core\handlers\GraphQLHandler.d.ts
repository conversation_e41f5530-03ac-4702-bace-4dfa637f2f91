import 'graphql';
export { E as ExpectedOperationTypeNode, G as GraphQLHandler, y as GraphQLHandlerInfo, n as GraphQLHandlerNameSelector, h as GraphQLJsonRequestBody, e as GraphQLQuery, g as GraphQLRequestBody, z as GraphQLRequestParsedResult, o as GraphQLResolverExtras, p as GraphQLResponseBody, f as GraphQLVariables, B as isDocumentNode } from '../HttpResponse-DWu36LsY.js';
import '../utils/matching/matchRequestUrl.js';
import '@mswjs/interceptors';
import '../utils/internal/isIterable.js';
import '../typeUtils.js';
