{"version": 3, "sources": ["../../../src/core/ws/WebSocketIndexedDBClientStore.ts"], "sourcesContent": ["import { DeferredPromise } from '@open-draft/deferred-promise'\nimport { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport {\n  type SerializedWebSocketClient,\n  WebSocketClientStore,\n} from './WebSocketClientStore'\n\nconst DB_NAME = 'msw-websocket-clients'\nconst DB_STORE_NAME = 'clients'\n\nexport class WebSocketIndexedDBClientStore implements WebSocketClientStore {\n  private db: Promise<IDBDatabase>\n\n  constructor() {\n    this.db = this.createDatabase()\n  }\n\n  public async add(client: WebSocketClientConnectionProtocol): Promise<void> {\n    const promise = new DeferredPromise<void>()\n    const store = await this.getStore()\n\n    /**\n     * @note Use `.put()` instead of `.add()` to allow setting clients\n     * that already exist in the database. This can happen if a single page\n     * has multiple event handlers. Each handler will receive the \"connection\"\n     * event in parallel, and try to set that WebSocket client in the database.\n     */\n    const request = store.put({\n      id: client.id,\n      url: client.url.href,\n    } satisfies SerializedWebSocketClient)\n\n    request.onsuccess = () => {\n      promise.resolve()\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(request.error)\n      promise.reject(\n        new Error(\n          `Failed to add WebSocket client \"${client.id}\". There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  public async getAll(): Promise<Array<SerializedWebSocketClient>> {\n    const promise = new DeferredPromise<Array<SerializedWebSocketClient>>()\n    const store = await this.getStore()\n    const request = store.getAll() as IDBRequest<\n      Array<SerializedWebSocketClient>\n    >\n\n    request.onsuccess = () => {\n      promise.resolve(request.result)\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.log(request.error)\n      promise.reject(\n        new Error(\n          `Failed to get all WebSocket clients. There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  public async deleteMany(clientIds: Array<string>): Promise<void> {\n    const promise = new DeferredPromise<void>()\n    const store = await this.getStore()\n\n    for (const clientId of clientIds) {\n      store.delete(clientId)\n    }\n\n    store.transaction.oncomplete = () => {\n      promise.resolve()\n    }\n    store.transaction.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(store.transaction.error)\n      promise.reject(\n        new Error(\n          `Failed to delete WebSocket clients [${clientIds.join(', ')}]. There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  private async createDatabase(): Promise<IDBDatabase> {\n    const promise = new DeferredPromise<IDBDatabase>()\n    const request = indexedDB.open(DB_NAME, 1)\n\n    request.onsuccess = ({ currentTarget }) => {\n      const db = Reflect.get(currentTarget!, 'result') as IDBDatabase\n\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return promise.resolve(db)\n      }\n    }\n\n    request.onupgradeneeded = async ({ currentTarget }) => {\n      const db = Reflect.get(currentTarget!, 'result') as IDBDatabase\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return\n      }\n\n      const store = db.createObjectStore(DB_STORE_NAME, { keyPath: 'id' })\n      store.transaction.oncomplete = () => {\n        promise.resolve(db)\n      }\n      store.transaction.onerror = () => {\n        // eslint-disable-next-line no-console\n        console.error(store.transaction.error)\n        promise.reject(\n          new Error(\n            'Failed to create WebSocket client store. There is likely an additional output above.',\n          ),\n        )\n      }\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(request.error)\n      promise.reject(\n        new Error(\n          'Failed to open an IndexedDB database. There is likely an additional output above.',\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  private async getStore(): Promise<IDBObjectStore> {\n    const db = await this.db\n    return db.transaction(DB_STORE_NAME, 'readwrite').objectStore(DB_STORE_NAME)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAAgC;AAOhC,MAAM,UAAU;AAChB,MAAM,gBAAgB;AAEf,MAAM,8BAA8D;AAAA,EACjE;AAAA,EAER,cAAc;AACZ,SAAK,KAAK,KAAK,eAAe;AAAA,EAChC;AAAA,EAEA,MAAa,IAAI,QAA0D;AACzE,UAAM,UAAU,IAAI,wCAAsB;AAC1C,UAAM,QAAQ,MAAM,KAAK,SAAS;AAQlC,UAAM,UAAU,MAAM,IAAI;AAAA,MACxB,IAAI,OAAO;AAAA,MACX,KAAK,OAAO,IAAI;AAAA,IAClB,CAAqC;AAErC,YAAQ,YAAY,MAAM;AACxB,cAAQ,QAAQ;AAAA,IAClB;AACA,YAAQ,UAAU,MAAM;AAEtB,cAAQ,MAAM,QAAQ,KAAK;AAC3B,cAAQ;AAAA,QACN,IAAI;AAAA,UACF,mCAAmC,OAAO,EAAE;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAa,SAAoD;AAC/D,UAAM,UAAU,IAAI,wCAAkD;AACtE,UAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,UAAM,UAAU,MAAM,OAAO;AAI7B,YAAQ,YAAY,MAAM;AACxB,cAAQ,QAAQ,QAAQ,MAAM;AAAA,IAChC;AACA,YAAQ,UAAU,MAAM;AAEtB,cAAQ,IAAI,QAAQ,KAAK;AACzB,cAAQ;AAAA,QACN,IAAI;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAa,WAAW,WAAyC;AAC/D,UAAM,UAAU,IAAI,wCAAsB;AAC1C,UAAM,QAAQ,MAAM,KAAK,SAAS;AAElC,eAAW,YAAY,WAAW;AAChC,YAAM,OAAO,QAAQ;AAAA,IACvB;AAEA,UAAM,YAAY,aAAa,MAAM;AACnC,cAAQ,QAAQ;AAAA,IAClB;AACA,UAAM,YAAY,UAAU,MAAM;AAEhC,cAAQ,MAAM,MAAM,YAAY,KAAK;AACrC,cAAQ;AAAA,QACN,IAAI;AAAA,UACF,uCAAuC,UAAU,KAAK,IAAI,CAAC;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAc,iBAAuC;AACnD,UAAM,UAAU,IAAI,wCAA6B;AACjD,UAAM,UAAU,UAAU,KAAK,SAAS,CAAC;AAEzC,YAAQ,YAAY,CAAC,EAAE,cAAc,MAAM;AACzC,YAAM,KAAK,QAAQ,IAAI,eAAgB,QAAQ;AAE/C,UAAI,GAAG,iBAAiB,SAAS,aAAa,GAAG;AAC/C,eAAO,QAAQ,QAAQ,EAAE;AAAA,MAC3B;AAAA,IACF;AAEA,YAAQ,kBAAkB,OAAO,EAAE,cAAc,MAAM;AACrD,YAAM,KAAK,QAAQ,IAAI,eAAgB,QAAQ;AAC/C,UAAI,GAAG,iBAAiB,SAAS,aAAa,GAAG;AAC/C;AAAA,MACF;AAEA,YAAM,QAAQ,GAAG,kBAAkB,eAAe,EAAE,SAAS,KAAK,CAAC;AACnE,YAAM,YAAY,aAAa,MAAM;AACnC,gBAAQ,QAAQ,EAAE;AAAA,MACpB;AACA,YAAM,YAAY,UAAU,MAAM;AAEhC,gBAAQ,MAAM,MAAM,YAAY,KAAK;AACrC,gBAAQ;AAAA,UACN,IAAI;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,YAAQ,UAAU,MAAM;AAEtB,cAAQ,MAAM,QAAQ,KAAK;AAC3B,cAAQ;AAAA,QACN,IAAI;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAc,WAAoC;AAChD,UAAM,KAAK,MAAM,KAAK;AACtB,WAAO,GAAG,YAAY,eAAe,WAAW,EAAE,YAAY,aAAa;AAAA,EAC7E;AACF;", "names": []}