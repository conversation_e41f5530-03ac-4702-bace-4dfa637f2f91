{"version": 3, "sources": ["../../../../src/core/utils/internal/isHandlerKind.ts"], "sourcesContent": ["import type { HandlerKind } from '../../handlers/common'\nimport type { RequestHandler } from '../../handlers/RequestHandler'\nimport type { WebSocketHandler } from '../../handlers/WebSocketHandler'\n\n/**\n * A filter function that ensures that the provided argument\n * is a handler of the given kind. This helps differentiate\n * between different kinds of handlers, e.g. request and event handlers.\n */\nexport function isHandlerKind<K extends HandlerKind>(kind: K) {\n  return (\n    input: unknown,\n  ): input is K extends 'EventHandler' ? WebSocketHandler : RequestHandler => {\n    return (\n      input != null &&\n      typeof input === 'object' &&\n      '__kind' in input &&\n      input.__kind === kind\n    )\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASO,SAAS,cAAqC,MAAS;AAC5D,SAAO,CACL,UAC0E;AAC1E,WACE,SAAS,QACT,OAAO,UAAU,YACjB,YAAY,SACZ,MAAM,WAAW;AAAA,EAErB;AACF;", "names": []}