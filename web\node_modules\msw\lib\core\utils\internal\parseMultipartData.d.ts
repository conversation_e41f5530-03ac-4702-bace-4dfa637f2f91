import { d as DefaultRequestMultipartBody } from '../../HttpResponse-DWu36LsY.js';
import '@mswjs/interceptors';
import './isIterable.js';
import '../../typeUtils.js';
import 'graphql';
import '../matching/matchRequestUrl.js';

/**
 * Parses a given string as a multipart/form-data.
 * Does not throw an exception on an invalid multipart string.
 */
declare function parseMultipartData<T extends DefaultRequestMultipartBody>(data: string, headers?: Headers): T | undefined;

export { parseMultipartData };
