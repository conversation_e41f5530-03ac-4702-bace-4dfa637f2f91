{"version": 3, "sources": ["../../../../src/core/utils/request/getRequestCookies.ts"], "sourcesContent": ["import cookieUtils from '@bundled-es-modules/cookie'\nimport { cookieStore } from '../cookieStore'\n\nfunction parseCookies(input: string): Record<string, string> {\n  const parsedCookies = cookieUtils.parse(input)\n  const cookies: Record<string, string> = {}\n\n  for (const cookieName in parsedCookies) {\n    if (typeof parsedCookies[cookieName] !== 'undefined') {\n      cookies[cookieName] = parsedCookies[cookieName]\n    }\n  }\n\n  return cookies\n}\n\nfunction getAllDocumentCookies() {\n  return parseCookies(document.cookie)\n}\n\nfunction getDocumentCookies(request: Request): Record<string, string> {\n  if (typeof document === 'undefined' || typeof location === 'undefined') {\n    return {}\n  }\n\n  switch (request.credentials) {\n    case 'same-origin': {\n      const requestUrl = new URL(request.url)\n\n      // Return document cookies only when requested a resource\n      // from the same origin as the current document.\n      return location.origin === requestUrl.origin\n        ? getAllDocumentCookies()\n        : {}\n    }\n\n    case 'include': {\n      // Return all document cookies.\n      return getAllDocumentCookies()\n    }\n\n    default: {\n      return {}\n    }\n  }\n}\n\nexport function getAllRequestCookies(request: Request): Record<string, string> {\n  /**\n   * @note While the \"cookie\" header is a forbidden header field\n   * in the browser, you can read it in Node.js. We need to respect\n   * it for mocking in Node.js.\n   */\n  const requestCookieHeader = request.headers.get('cookie')\n  const cookiesFromHeaders = requestCookieHeader\n    ? parseCookies(requestCookieHeader)\n    : {}\n\n  const cookiesFromDocument = getDocumentCookies(request)\n\n  // Forward the document cookies to the request headers.\n  for (const name in cookiesFromDocument) {\n    request.headers.append(\n      'cookie',\n      cookieUtils.serialize(name, cookiesFromDocument[name]),\n    )\n  }\n\n  const cookiesFromStore = cookieStore.getCookiesSync(request.url)\n  const storedCookiesObject = Object.fromEntries(\n    cookiesFromStore.map((cookie) => [cookie.key, cookie.value]),\n  )\n\n  // Forward the raw stored cookies to request headers\n  // so they contain metadata like \"expires\", \"secure\", etc.\n  for (const cookie of cookiesFromStore) {\n    request.headers.append('cookie', cookie.toString())\n  }\n\n  return {\n    ...cookiesFromDocument,\n    ...storedCookiesObject,\n    ...cookiesFromHeaders,\n  }\n}\n"], "mappings": "AAAA,OAAO,iBAAiB;AACxB,SAAS,mBAAmB;AAE5B,SAAS,aAAa,OAAuC;AAC3D,QAAM,gBAAgB,YAAY,MAAM,KAAK;AAC7C,QAAM,UAAkC,CAAC;AAEzC,aAAW,cAAc,eAAe;AACtC,QAAI,OAAO,cAAc,UAAU,MAAM,aAAa;AACpD,cAAQ,UAAU,IAAI,cAAc,UAAU;AAAA,IAChD;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,wBAAwB;AAC/B,SAAO,aAAa,SAAS,MAAM;AACrC;AAEA,SAAS,mBAAmB,SAA0C;AACpE,MAAI,OAAO,aAAa,eAAe,OAAO,aAAa,aAAa;AACtE,WAAO,CAAC;AAAA,EACV;AAEA,UAAQ,QAAQ,aAAa;AAAA,IAC3B,KAAK,eAAe;AAClB,YAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;AAItC,aAAO,SAAS,WAAW,WAAW,SAClC,sBAAsB,IACtB,CAAC;AAAA,IACP;AAAA,IAEA,KAAK,WAAW;AAEd,aAAO,sBAAsB;AAAA,IAC/B;AAAA,IAEA,SAAS;AACP,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEO,SAAS,qBAAqB,SAA0C;AAM7E,QAAM,sBAAsB,QAAQ,QAAQ,IAAI,QAAQ;AACxD,QAAM,qBAAqB,sBACvB,aAAa,mBAAmB,IAChC,CAAC;AAEL,QAAM,sBAAsB,mBAAmB,OAAO;AAGtD,aAAW,QAAQ,qBAAqB;AACtC,YAAQ,QAAQ;AAAA,MACd;AAAA,MACA,YAAY,UAAU,MAAM,oBAAoB,IAAI,CAAC;AAAA,IACvD;AAAA,EACF;AAEA,QAAM,mBAAmB,YAAY,eAAe,QAAQ,GAAG;AAC/D,QAAM,sBAAsB,OAAO;AAAA,IACjC,iBAAiB,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,EAC7D;AAIA,aAAW,UAAU,kBAAkB;AACrC,YAAQ,QAAQ,OAAO,UAAU,OAAO,SAAS,CAAC;AAAA,EACpD;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;", "names": []}