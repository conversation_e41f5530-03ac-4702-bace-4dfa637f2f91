import { ApiError } from '@/types/auth';

/**
 * Formatar mensagem de erro para exibição ao usuário
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'object' && error !== null && 'message' in error) {
    const apiError = error as ApiError;
    return apiError.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  return 'Ocorreu um erro inesperado';
}

/**
 * Verificar se é um erro de rede
 */
export function isNetworkError(error: unknown): boolean {
  if (error instanceof Error) {
    return error.message.toLowerCase().includes('network') ||
           error.message.toLowerCase().includes('fetch');
  }

  if (typeof error === 'object' && error !== null && 'statusCode' in error) {
    const apiError = error as ApiError;
    return apiError.statusCode >= 500;
  }

  return false;
}

/**
 * Verificar se é um erro de autenticação
 */
export function isAuthError(error: unknown): boolean {
  if (typeof error === 'object' && error !== null && 'statusCode' in error) {
    const apiError = error as ApiError;
    return apiError.statusCode === 401 || apiError.statusCode === 403;
  }

  return false;
}

/**
 * Verificar se é um erro de validação
 */
export function isValidationError(error: unknown): boolean {
  if (typeof error === 'object' && error !== null && 'statusCode' in error) {
    const apiError = error as ApiError;
    return apiError.statusCode === 400 || apiError.statusCode === 422;
  }

  return false;
}

/**
 * Obter mensagem de erro amigável baseada no tipo
 */
export function getFriendlyErrorMessage(error: unknown): string {
  if (isNetworkError(error)) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }

  if (isAuthError(error)) {
    return 'Sessão expirada. Faça login novamente.';
  }

  if (isValidationError(error)) {
    return formatErrorMessage(error);
  }

  return 'Ocorreu um erro inesperado. Tente novamente.';
}
