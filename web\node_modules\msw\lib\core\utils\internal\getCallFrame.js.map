{"version": 3, "sources": ["../../../../src/core/utils/internal/getCallFrame.ts"], "sourcesContent": ["// Ignore the source files traces for local testing.\nconst SOURCE_FRAME = /[\\/\\\\]msw[\\/\\\\]src[\\/\\\\](.+)/\n\nconst BUILD_FRAME =\n  /(node_modules)?[\\/\\\\]lib[\\/\\\\](core|browser|node|native|iife)[\\/\\\\]|^[^\\/\\\\]*$/\n\n/**\n * Return the stack trace frame of a function's invocation.\n */\nexport function getCallFrame(error: Error) {\n  // In <IE11, new Error may return an undefined stack\n  const stack = error.stack\n\n  if (!stack) {\n    return\n  }\n\n  const frames: string[] = stack.split('\\n').slice(1)\n\n  // Get the first frame that doesn't reference the library's internal trace.\n  // Assume that frame is the invocation frame.\n  const declarationFrame = frames.find((frame) => {\n    return !(SOURCE_FRAME.test(frame) || BUILD_FRAME.test(frame))\n  })\n\n  if (!declarationFrame) {\n    return\n  }\n\n  // Extract file reference from the stack frame.\n  const declarationPath = declarationFrame\n    .replace(/\\s*at [^()]*\\(([^)]+)\\)/, '$1')\n    .replace(/^@/, '')\n  return declarationPath\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,MAAM,eAAe;AAErB,MAAM,cACJ;AAKK,SAAS,aAAa,OAAc;AAEzC,QAAM,QAAQ,MAAM;AAEpB,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAEA,QAAM,SAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,CAAC;AAIlD,QAAM,mBAAmB,OAAO,KAAK,CAAC,UAAU;AAC9C,WAAO,EAAE,aAAa,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK;AAAA,EAC7D,CAAC;AAED,MAAI,CAAC,kBAAkB;AACrB;AAAA,EACF;AAGA,QAAM,kBAAkB,iBACrB,QAAQ,2BAA2B,IAAI,EACvC,QAAQ,MAAM,EAAE;AACnB,SAAO;AACT;", "names": []}