{"version": 3, "sources": ["../../../../src/core/utils/internal/devUtils.ts"], "sourcesContent": ["import { format } from 'outvariant'\n\nconst LIBRARY_PREFIX = '[MSW]'\n\n/**\n * Formats a given message by appending the library's prefix string.\n */\nfunction formatMessage(message: string, ...positionals: any[]): string {\n  const interpolatedMessage = format(message, ...positionals)\n  return `${LIBRARY_PREFIX} ${interpolatedMessage}`\n}\n\n/**\n * Prints a library-specific warning.\n */\nfunction warn(message: string, ...positionals: any[]): void {\n  // eslint-disable-next-line no-console\n  console.warn(formatMessage(message, ...positionals))\n}\n\n/**\n * Prints a library-specific error.\n */\nfunction error(message: string, ...positionals: any[]): void {\n  // eslint-disable-next-line no-console\n  console.error(formatMessage(message, ...positionals))\n}\n\nexport const devUtils = {\n  formatMessage,\n  warn,\n  error,\n}\n\n/**\n * Internal error instance.\n * Used to differentiate the library errors that must be forwarded\n * to the user from the unhandled exceptions. Use this if you don't\n * wish for the error to be coerced to a 500 fallback response.\n */\nexport class InternalError extends Error {\n  constructor(message: string) {\n    super(message)\n    this.name = 'InternalError'\n  }\n}\n"], "mappings": "AAAA,SAAS,cAAc;AAEvB,MAAM,iBAAiB;AAKvB,SAAS,cAAc,YAAoB,aAA4B;AACrE,QAAM,sBAAsB,OAAO,SAAS,GAAG,WAAW;AAC1D,SAAO,GAAG,cAAc,IAAI,mBAAmB;AACjD;AAKA,SAAS,KAAK,YAAoB,aAA0B;AAE1D,UAAQ,KAAK,cAAc,SAAS,GAAG,WAAW,CAAC;AACrD;AAKA,SAAS,MAAM,YAAoB,aAA0B;AAE3D,UAAQ,MAAM,cAAc,SAAS,GAAG,WAAW,CAAC;AACtD;AAEO,MAAM,WAAW;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF;AAQO,MAAM,sBAAsB,MAAM;AAAA,EACvC,YAAY,SAAiB;AAC3B,UAAM,OAAO;AACb,SAAK,OAAO;AAAA,EACd;AACF;", "names": []}