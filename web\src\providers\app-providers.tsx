import { ReactNode } from 'react';
import { RouterProvider } from 'react-router-dom';
import { QueryProvider } from './query-provider';
import { router } from '@/router';

interface AppProvidersProps {
  children?: ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <QueryProvider>
      {children ? children : <RouterProvider router={router} />}
    </QueryProvider>
  );
}
