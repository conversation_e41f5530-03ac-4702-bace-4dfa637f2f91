{"version": 3, "sources": ["../../../../src/core/utils/request/storeResponseCookies.ts"], "sourcesContent": ["import { cookieStore } from '../cookieStore'\nimport { kSetCookie } from '../HttpResponse/decorators'\n\nexport function storeResponseCookies(\n  request: Request,\n  response: Response,\n): void {\n  // Grab the raw \"Set-Cookie\" response header provided\n  // in the HeadersInit for this mocked response.\n  const responseCookies = Reflect.get(response, kSetCookie) as\n    | string\n    | undefined\n\n  if (responseCookies) {\n    cookieStore.setCookie(responseCookies, request.url)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAA4B;AAC5B,wBAA2B;AAEpB,SAAS,qBACd,SACA,UACM;AAGN,QAAM,kBAAkB,QAAQ,IAAI,UAAU,4BAAU;AAIxD,MAAI,iBAAiB;AACnB,mCAAY,UAAU,iBAAiB,QAAQ,GAAG;AAAA,EACpD;AACF;", "names": []}