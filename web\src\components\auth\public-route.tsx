import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useIsAuthenticated } from '@/hooks/use-auth';
import { LoadingOverlay } from '@/components/ui/loading';

interface PublicRouteProps {
  children: ReactNode;
}

export function PublicRoute({ children }: PublicRouteProps) {
  const { isAuthenticated, isLoading } = useIsAuthenticated();
  const location = useLocation();

  // Mostrar loading enquanto verifica autenticação
  if (isLoading) {
    return <LoadingOverlay text="Verificando autenticação..." />;
  }

  // Se estiver autenticado, redirecionar para dashboard
  if (isAuthenticated) {
    // Verificar se há uma rota de origem para redirecionar
    const from = location.state?.from?.pathname || '/dashboard';
    return <Navigate to={from} replace />;
  }

  // Se não estiver autenticado, renderizar o conteúdo público
  return <>{children}</>;
}
