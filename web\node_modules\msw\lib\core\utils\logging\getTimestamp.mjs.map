{"version": 3, "sources": ["../../../../src/core/utils/logging/getTimestamp.ts"], "sourcesContent": ["interface GetTimestampOptions {\n  milliseconds?: boolean\n}\n\n/**\n * Returns a timestamp string in a \"HH:MM:SS\" format.\n */\nexport function getTimestamp(options?: GetTimestampOptions): string {\n  const now = new Date()\n  const timestamp = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`\n\n  if (options?.milliseconds) {\n    return `${timestamp}.${now.getMilliseconds().toString().padStart(3, '0')}`\n  }\n\n  return timestamp\n}\n"], "mappings": "AAOO,SAAS,aAAa,SAAuC;AAClE,QAAM,MAAM,oBAAI,KAAK;AACrB,QAAM,YAAY,GAAG,IAAI,SAAS,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,WAAW,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,WAAW,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAE/J,MAAI,SAAS,cAAc;AACzB,WAAO,GAAG,SAAS,IAAI,IAAI,gBAAgB,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,EAC1E;AAEA,SAAO;AACT;", "names": []}