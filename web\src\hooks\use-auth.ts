import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { AuthService } from '@/services/auth.service';
import {
  LoginRequest,
  SetPasswordRequest,
  TwoFactorVerifyRequest,
  ApiError,
} from '@/types/auth';

// Query keys
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
};

/**
 * Hook para obter dados do usuário atual
 */
export function useCurrentUser() {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: AuthService.getCurrentUser,
    enabled: AuthService.isAuthenticated(),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para login
 */
export function useLogin() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: LoginRequest) => AuthService.login(credentials),
    onSuccess: (data) => {
      // Atualizar cache do usuário
      queryClient.setQueryData(authKeys.user(), data.user);
      
      // Redirecionar baseado no status
      if (data.requiresTwoFactor) {
        navigate('/auth/2fa');
      } else {
        navigate('/dashboard');
      }
    },
    onError: (error: ApiError) => {
      console.error('Erro no login:', error);
    },
  });
}

/**
 * Hook para logout
 */
export function useLogout() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: AuthService.logout,
    onSuccess: () => {
      // Limpar todos os dados do cache
      queryClient.clear();
      
      // Redirecionar para login
      navigate('/login');
    },
    onError: (error: ApiError) => {
      console.error('Erro no logout:', error);
      
      // Mesmo com erro, limpar dados locais e redirecionar
      queryClient.clear();
      navigate('/login');
    },
  });
}

/**
 * Hook para definir senha
 */
export function useSetPassword() {
  const navigate = useNavigate();

  return useMutation({
    mutationFn: (data: SetPasswordRequest) => AuthService.setPassword(data),
    onSuccess: () => {
      navigate('/login', {
        state: { message: 'Senha definida com sucesso! Faça login.' },
      });
    },
    onError: (error: ApiError) => {
      console.error('Erro ao definir senha:', error);
    },
  });
}

/**
 * Hook para configurar 2FA
 */
export function useSetup2FA() {
  return useMutation({
    mutationFn: AuthService.setup2FA,
    onError: (error: ApiError) => {
      console.error('Erro ao configurar 2FA:', error);
    },
  });
}

/**
 * Hook para verificar 2FA
 */
export function useVerify2FA() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: TwoFactorVerifyRequest) => AuthService.verify2FA(data),
    onSuccess: () => {
      // Invalidar cache do usuário para recarregar dados
      queryClient.invalidateQueries({ queryKey: authKeys.user() });
      
      navigate('/dashboard');
    },
    onError: (error: ApiError) => {
      console.error('Erro na verificação 2FA:', error);
    },
  });
}

/**
 * Hook para desabilitar 2FA
 */
export function useDisable2FA() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (token: string) => AuthService.disable2FA(token),
    onSuccess: () => {
      // Invalidar cache do usuário para recarregar dados
      queryClient.invalidateQueries({ queryKey: authKeys.user() });
    },
    onError: (error: ApiError) => {
      console.error('Erro ao desabilitar 2FA:', error);
    },
  });
}

/**
 * Hook para verificar se está autenticado
 */
export function useIsAuthenticated() {
  const { data: user, isLoading } = useCurrentUser();
  
  return {
    isAuthenticated: !!user && AuthService.isAuthenticated(),
    isLoading,
    user,
  };
}
