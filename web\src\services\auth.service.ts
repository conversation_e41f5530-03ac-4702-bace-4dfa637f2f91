import api from '@/lib/api';
import {
  LoginRequest,
  LoginResponse,
  SetPasswordRequest,
  RefreshTokenRequest,
  TwoFactorSetupResponse,
  TwoFactorVerifyRequest,
  User,
} from '@/types/auth';

export class AuthService {
  /**
   * Realizar login
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/auth/login', credentials);
    
    // Salvar token no localStorage para uso nos interceptors
    if (response.data.accessToken) {
      localStorage.setItem('accessToken', response.data.accessToken);
    }
    
    return response.data;
  }

  /**
   * Realizar logout
   */
  static async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } finally {
      // Sempre limpar dados locais, mesmo se a API falhar
      localStorage.removeItem('accessToken');
    }
  }

  /**
   * Refresh do token
   */
  static async refreshToken(): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/auth/refresh');
    
    if (response.data.accessToken) {
      localStorage.setItem('accessToken', response.data.accessToken);
    }
    
    return response.data;
  }

  /**
   * Definir senha (primeiro acesso)
   */
  static async setPassword(data: SetPasswordRequest): Promise<void> {
    await api.post('/auth/set-password', {
      token: data.token,
      password: data.password,
    });
  }

  /**
   * Configurar 2FA
   */
  static async setup2FA(): Promise<TwoFactorSetupResponse> {
    const response = await api.post<TwoFactorSetupResponse>('/auth/2fa/setup');
    return response.data;
  }

  /**
   * Verificar código 2FA
   */
  static async verify2FA(data: TwoFactorVerifyRequest): Promise<void> {
    await api.post('/auth/2fa/verify', data);
  }

  /**
   * Desabilitar 2FA
   */
  static async disable2FA(token: string): Promise<void> {
    await api.post('/auth/2fa/disable', { token });
  }

  /**
   * Obter usuário atual
   */
  static async getCurrentUser(): Promise<User> {
    const response = await api.get<User>('/auth/me');
    return response.data;
  }

  /**
   * Verificar se o usuário está autenticado
   */
  static isAuthenticated(): boolean {
    return !!localStorage.getItem('accessToken');
  }

  /**
   * Obter token do localStorage
   */
  static getToken(): string | null {
    return localStorage.getItem('accessToken');
  }
}
