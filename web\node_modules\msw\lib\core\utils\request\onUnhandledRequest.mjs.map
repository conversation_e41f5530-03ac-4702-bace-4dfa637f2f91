{"version": 3, "sources": ["../../../../src/core/utils/request/onUnhandledRequest.ts"], "sourcesContent": ["import { toPublicUrl } from './toPublicUrl'\nimport { InternalError, devUtils } from '../internal/devUtils'\nimport { isCommonAssetRequest } from '../../isCommonAssetRequest'\n\nexport interface UnhandledRequestPrint {\n  warning(): void\n  error(): void\n}\n\nexport type UnhandledRequestCallback = (\n  request: Request,\n  print: UnhandledRequestPrint,\n) => void\n\nexport type UnhandledRequestStrategy =\n  | 'bypass'\n  | 'warn'\n  | 'error'\n  | UnhandledRequestCallback\n\nexport async function onUnhandledRequest(\n  request: Request,\n  strategy: UnhandledRequestStrategy = 'warn',\n): Promise<void> {\n  const url = new URL(request.url)\n  const publicUrl = toPublicUrl(url) + url.search\n\n  const requestBody =\n    request.method === 'HEAD' || request.method === 'GET'\n      ? null\n      : await request.clone().text()\n  const messageDetails = `\\n\\n  \\u2022 ${request.method} ${publicUrl}\\n\\n${requestBody ? `  \\u2022 Request body: ${requestBody}\\n\\n` : ''}`\n  const unhandledRequestMessage = `intercepted a request without a matching request handler:${messageDetails}If you still wish to intercept this unhandled request, please create a request handler for it.\\nRead more: https://mswjs.io/docs/http/intercepting-requests`\n\n  function applyStrategy(strategy: UnhandledRequestStrategy) {\n    switch (strategy) {\n      case 'error': {\n        // Print a developer-friendly error.\n        devUtils.error('Error: %s', unhandledRequestMessage)\n\n        // Throw an exception to halt request processing and not perform the original request.\n        throw new InternalError(\n          devUtils.formatMessage(\n            'Cannot bypass a request when using the \"error\" strategy for the \"onUnhandledRequest\" option.',\n          ),\n        )\n      }\n\n      case 'warn': {\n        devUtils.warn('Warning: %s', unhandledRequestMessage)\n        break\n      }\n\n      case 'bypass':\n        break\n\n      default:\n        throw new InternalError(\n          devUtils.formatMessage(\n            'Failed to react to an unhandled request: unknown strategy \"%s\". Please provide one of the supported strategies (\"bypass\", \"warn\", \"error\") or a custom callback function as the value of the \"onUnhandledRequest\" option.',\n            strategy,\n          ),\n        )\n    }\n  }\n\n  if (typeof strategy === 'function') {\n    strategy(request, {\n      warning: applyStrategy.bind(null, 'warn'),\n      error: applyStrategy.bind(null, 'error'),\n    })\n    return\n  }\n\n  // Ignore common static asset requests when using a built-in strategy.\n  // There's a slight overhead here because this utility will create a request URL\n  // instance again despite us having done so previously in this function.\n  if (!isCommonAssetRequest(request)) {\n    applyStrategy(strategy)\n  }\n}\n"], "mappings": "AAAA,SAAS,mBAAmB;AAC5B,SAAS,eAAe,gBAAgB;AACxC,SAAS,4BAA4B;AAkBrC,eAAsB,mBACpB,SACA,WAAqC,QACtB;AACf,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,YAAY,YAAY,GAAG,IAAI,IAAI;AAEzC,QAAM,cACJ,QAAQ,WAAW,UAAU,QAAQ,WAAW,QAC5C,OACA,MAAM,QAAQ,MAAM,EAAE,KAAK;AACjC,QAAM,iBAAiB;AAAA;AAAA,WAAgB,QAAQ,MAAM,IAAI,SAAS;AAAA;AAAA,EAAO,cAAc,0BAA0B,WAAW;AAAA;AAAA,IAAS,EAAE;AACvI,QAAM,0BAA0B,4DAA4D,cAAc;AAAA;AAE1G,WAAS,cAAcA,WAAoC;AACzD,YAAQA,WAAU;AAAA,MAChB,KAAK,SAAS;AAEZ,iBAAS,MAAM,aAAa,uBAAuB;AAGnD,cAAM,IAAI;AAAA,UACR,SAAS;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,iBAAS,KAAK,eAAe,uBAAuB;AACpD;AAAA,MACF;AAAA,MAEA,KAAK;AACH;AAAA,MAEF;AACE,cAAM,IAAI;AAAA,UACR,SAAS;AAAA,YACP;AAAA,YACAA;AAAA,UACF;AAAA,QACF;AAAA,IACJ;AAAA,EACF;AAEA,MAAI,OAAO,aAAa,YAAY;AAClC,aAAS,SAAS;AAAA,MAChB,SAAS,cAAc,KAAK,MAAM,MAAM;AAAA,MACxC,OAAO,cAAc,KAAK,MAAM,OAAO;AAAA,IACzC,CAAC;AACD;AAAA,EACF;AAKA,MAAI,CAAC,qBAAqB,OAAO,GAAG;AAClC,kBAAc,QAAQ;AAAA,EACxB;AACF;", "names": ["strategy"]}