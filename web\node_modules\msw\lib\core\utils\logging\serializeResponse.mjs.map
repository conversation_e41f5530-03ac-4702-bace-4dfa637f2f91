{"version": 3, "sources": ["../../../../src/core/utils/logging/serializeResponse.ts"], "sourcesContent": ["import statuses from '@bundled-es-modules/statuses'\n\nconst { message } = statuses\n\nexport interface SerializedResponse {\n  status: number\n  statusText: string\n  headers: Record<string, any>\n  body: string\n}\n\nexport async function serializeResponse(\n  response: Response,\n): Promise<SerializedResponse> {\n  const responseClone = response.clone()\n  const responseText = await responseClone.text()\n\n  // Normalize the response status and status text when logging\n  // since the default Response instance doesn't infer status texts\n  // from status codes. This has no effect on the actual response instance.\n  const responseStatus = responseClone.status || 200\n  const responseStatusText =\n    responseClone.statusText || message[responseStatus] || 'OK'\n\n  return {\n    status: responseStatus,\n    statusText: responseStatusText,\n    headers: Object.fromEntries(responseClone.headers.entries()),\n    body: responseText,\n  }\n}\n"], "mappings": "AAAA,OAAO,cAAc;AAErB,MAAM,EAAE,QAAQ,IAAI;AASpB,eAAsB,kBACpB,UAC6B;AAC7B,QAAM,gBAAgB,SAAS,MAAM;AACrC,QAAM,eAAe,MAAM,cAAc,KAAK;AAK9C,QAAM,iBAAiB,cAAc,UAAU;AAC/C,QAAM,qBACJ,cAAc,cAAc,QAAQ,cAAc,KAAK;AAEzD,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,SAAS,OAAO,YAAY,cAAc,QAAQ,QAAQ,CAAC;AAAA,IAC3D,MAAM;AAAA,EACR;AACF;", "names": []}