import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Shield, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LoadingSpinner } from '@/components/ui/loading';

import { useVerify2FA } from '@/hooks/use-auth';
import { twoFactorSchema, TwoFactorFormData } from '@/lib/validations/auth';

export function TwoFactorForm() {
  const verify2FAMutation = useVerify2FA();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<TwoFactorFormData>({
    resolver: zodResolver(twoFactorSchema),
  });

  const token = watch('token');

  const onSubmit = (data: TwoFactorFormData) => {
    verify2FAMutation.mutate(data);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <Shield className="h-6 w-6 text-primary" />
        </div>
        <h1 className="text-3xl font-bold">Verificação em Duas Etapas</h1>
        <p className="text-muted-foreground">
          Digite o código de 6 dígitos do seu aplicativo autenticador
        </p>
      </div>

      {verify2FAMutation.error && (
        <Alert variant="destructive">
          <AlertDescription>
            {verify2FAMutation.error.message || 'Código inválido'}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="token">Código de Verificação</Label>
          <Input
            id="token"
            type="text"
            placeholder="000000"
            maxLength={6}
            className="text-center text-2xl tracking-widest"
            {...register('token')}
            disabled={verify2FAMutation.isPending}
            autoComplete="one-time-code"
          />
          {errors.token && (
            <p className="text-sm text-destructive">{errors.token.message}</p>
          )}
          
          {/* Indicador visual do progresso */}
          <div className="flex justify-center gap-1">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className={`h-2 w-2 rounded-full ${
                  index < (token?.length || 0)
                    ? 'bg-primary'
                    : 'bg-muted'
                }`}
              />
            ))}
          </div>
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={verify2FAMutation.isPending || (token?.length || 0) < 6}
        >
          {verify2FAMutation.isPending ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Verificando...
            </>
          ) : (
            <>
              <Shield className="mr-2 h-4 w-4" />
              Verificar Código
            </>
          )}
        </Button>
      </form>

      <div className="space-y-4 text-center text-sm">
        <div className="rounded-lg bg-muted/50 p-4">
          <p className="text-muted-foreground">
            <strong>Não consegue acessar seu aplicativo?</strong>
            <br />
            Entre em contato com o administrador do sistema para obter ajuda.
          </p>
        </div>

        <Link
          to="/login"
          className="inline-flex items-center font-medium text-primary underline-offset-4 hover:underline"
        >
          <ArrowLeft className="mr-1 h-3 w-3" />
          Voltar ao login
        </Link>
      </div>
    </div>
  );
}
