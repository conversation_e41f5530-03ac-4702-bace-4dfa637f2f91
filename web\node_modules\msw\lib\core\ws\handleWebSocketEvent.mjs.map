{"version": 3, "sources": ["../../../src/core/ws/handleWebSocketEvent.ts"], "sourcesContent": ["import type { WebSocketConnectionData } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport { RequestHandler } from '../handlers/RequestHandler'\nimport { WebSocketHandler } from '../handlers/WebSocketHandler'\nimport { webSocketInterceptor } from './webSocketInterceptor'\nimport {\n  onUnhandledRequest,\n  UnhandledRequestStrategy,\n} from '../utils/request/onUnhandledRequest'\nimport { isHandlerKind } from '../utils/internal/isHandlerKind'\n\ninterface HandleWebSocketEventOptions {\n  getUnhandledRequestStrategy: () => UnhandledRequestStrategy\n  getHandlers: () => Array<RequestHandler | WebSocketHandler>\n  onMockedConnection: (connection: WebSocketConnectionData) => void\n  onPassthroughConnection: (onnection: WebSocketConnectionData) => void\n}\n\nexport function handleWebSocketEvent(options: HandleWebSocketEventOptions) {\n  webSocketInterceptor.on('connection', async (connection) => {\n    const handlers = options.getHandlers().filter(isHandlerKind('EventHandler'))\n\n    // Ignore this connection if the user hasn't defined any handlers.\n    if (handlers.length > 0) {\n      options?.onMockedConnection(connection)\n\n      await Promise.all(\n        handlers.map((handler) => {\n          // Iterate over the handlers and forward the connection\n          // event to WebSocket event handlers. This is equivalent\n          // to dispatching that event onto multiple listeners.\n          return handler.run(connection)\n        }),\n      )\n\n      return\n    }\n\n    // Construct a request representing this WebSocket connection.\n    const request = new Request(connection.client.url, {\n      headers: {\n        upgrade: 'websocket',\n        connection: 'upgrade',\n      },\n    })\n    await onUnhandledRequest(\n      request,\n      options.getUnhandledRequestStrategy(),\n    ).catch((error) => {\n      const errorEvent = new Event('error')\n      Object.defineProperty(errorEvent, 'cause', {\n        enumerable: true,\n        configurable: false,\n        value: error,\n      })\n      connection.client.socket.dispatchEvent(errorEvent)\n    })\n\n    options?.onPassthroughConnection(connection)\n\n    // If none of the \"ws\" handlers matched,\n    // establish the WebSocket connection as-is.\n    connection.server.connect()\n  })\n}\n"], "mappings": "AAGA,SAAS,4BAA4B;AACrC;AAAA,EACE;AAAA,OAEK;AACP,SAAS,qBAAqB;AASvB,SAAS,qBAAqB,SAAsC;AACzE,uBAAqB,GAAG,cAAc,OAAO,eAAe;AAC1D,UAAM,WAAW,QAAQ,YAAY,EAAE,OAAO,cAAc,cAAc,CAAC;AAG3E,QAAI,SAAS,SAAS,GAAG;AACvB,eAAS,mBAAmB,UAAU;AAEtC,YAAM,QAAQ;AAAA,QACZ,SAAS,IAAI,CAAC,YAAY;AAIxB,iBAAO,QAAQ,IAAI,UAAU;AAAA,QAC/B,CAAC;AAAA,MACH;AAEA;AAAA,IACF;AAGA,UAAM,UAAU,IAAI,QAAQ,WAAW,OAAO,KAAK;AAAA,MACjD,SAAS;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ,4BAA4B;AAAA,IACtC,EAAE,MAAM,CAAC,UAAU;AACjB,YAAM,aAAa,IAAI,MAAM,OAAO;AACpC,aAAO,eAAe,YAAY,SAAS;AAAA,QACzC,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AACD,iBAAW,OAAO,OAAO,cAAc,UAAU;AAAA,IACnD,CAAC;AAED,aAAS,wBAAwB,UAAU;AAI3C,eAAW,OAAO,QAAQ;AAAA,EAC5B,CAAC;AACH;", "names": []}