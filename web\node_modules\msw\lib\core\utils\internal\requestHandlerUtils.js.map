{"version": 3, "sources": ["../../../../src/core/utils/internal/requestHandlerUtils.ts"], "sourcesContent": ["import { RequestHandler } from '../../handlers/RequestHandler'\n\nexport function use(\n  currentHandlers: Array<RequestHandler>,\n  ...handlers: Array<RequestHandler>\n): void {\n  currentHandlers.unshift(...handlers)\n}\n\nexport function restoreHandlers(handlers: Array<RequestHandler>): void {\n  handlers.forEach((handler) => {\n    handler.isUsed = false\n  })\n}\n\nexport function resetHandlers(\n  initialHandlers: Array<RequestHandler>,\n  ...nextHandlers: Array<RequestHandler>\n) {\n  return nextHandlers.length > 0 ? [...nextHandlers] : [...initialHandlers]\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,SAAS,IACd,oBACG,UACG;AACN,kBAAgB,QAAQ,GAAG,QAAQ;AACrC;AAEO,SAAS,gBAAgB,UAAuC;AACrE,WAAS,QAAQ,CAAC,YAAY;AAC5B,YAAQ,SAAS;AAAA,EACnB,CAAC;AACH;AAEO,SAAS,cACd,oBACG,cACH;AACA,SAAO,aAAa,SAAS,IAAI,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,eAAe;AAC1E;", "names": []}