import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Key, ArrowLeft } from 'lucide-react';
import { Link, useSearchParams } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LoadingSpinner } from '@/components/ui/loading';

import { useSetPassword } from '@/hooks/use-auth';
import { setPasswordSchema, SetPasswordFormData } from '@/lib/validations/auth';

export function SetPasswordForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [searchParams] = useSearchParams();
  const setPasswordMutation = useSetPassword();

  // Token vindo da URL
  const tokenFromUrl = searchParams.get('token') || '';

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SetPasswordFormData>({
    resolver: zodResolver(setPasswordSchema),
    defaultValues: {
      token: tokenFromUrl,
    },
  });

  const password = watch('password');

  const onSubmit = (data: SetPasswordFormData) => {
    setPasswordMutation.mutate(data);
  };

  // Validações visuais da senha
  const passwordValidations = [
    { test: password?.length >= 8, text: 'Pelo menos 8 caracteres' },
    { test: /[a-z]/.test(password || ''), text: 'Uma letra minúscula' },
    { test: /[A-Z]/.test(password || ''), text: 'Uma letra maiúscula' },
    { test: /\d/.test(password || ''), text: 'Um número' },
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Definir Senha</h1>
        <p className="text-muted-foreground">
          Crie uma senha segura para acessar sua conta
        </p>
      </div>

      {setPasswordMutation.error && (
        <Alert variant="destructive">
          <AlertDescription>
            {setPasswordMutation.error.message || 'Erro ao definir senha'}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="token">Token de Acesso</Label>
          <Input
            id="token"
            type="text"
            placeholder="Token recebido por email"
            {...register('token')}
            disabled={setPasswordMutation.isPending}
          />
          {errors.token && (
            <p className="text-sm text-destructive">{errors.token.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Nova Senha</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Sua nova senha"
              {...register('password')}
              disabled={setPasswordMutation.isPending}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              disabled={setPasswordMutation.isPending}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className="text-sm text-destructive">{errors.password.message}</p>
          )}
          
          {/* Indicadores de validação da senha */}
          {password && (
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Sua senha deve conter:</p>
              {passwordValidations.map((validation, index) => (
                <div key={index} className="flex items-center gap-2 text-xs">
                  <div
                    className={`h-2 w-2 rounded-full ${
                      validation.test ? 'bg-green-500' : 'bg-gray-300'
                    }`}
                  />
                  <span
                    className={
                      validation.test ? 'text-green-600' : 'text-muted-foreground'
                    }
                  >
                    {validation.text}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirmar Senha</Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Confirme sua senha"
              {...register('confirmPassword')}
              disabled={setPasswordMutation.isPending}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={setPasswordMutation.isPending}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-destructive">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={setPasswordMutation.isPending}
        >
          {setPasswordMutation.isPending ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Definindo senha...
            </>
          ) : (
            <>
              <Key className="mr-2 h-4 w-4" />
              Definir Senha
            </>
          )}
        </Button>
      </form>

      <div className="text-center text-sm">
        <Link
          to="/login"
          className="inline-flex items-center font-medium text-primary underline-offset-4 hover:underline"
        >
          <ArrowLeft className="mr-1 h-3 w-3" />
          Voltar ao login
        </Link>
      </div>
    </div>
  );
}
