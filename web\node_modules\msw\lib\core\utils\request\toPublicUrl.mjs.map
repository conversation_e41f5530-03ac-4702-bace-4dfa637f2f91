{"version": 3, "sources": ["../../../../src/core/utils/request/toPublicUrl.ts"], "sourcesContent": ["/**\n * Returns a relative URL if the given request URL is relative\n * to the current origin. Otherwise returns an absolute URL.\n */\nexport function toPublicUrl(url: string | URL): string {\n  if (typeof location === 'undefined') {\n    return url.toString()\n  }\n\n  const urlInstance = url instanceof URL ? url : new URL(url)\n\n  return urlInstance.origin === location.origin\n    ? urlInstance.pathname\n    : urlInstance.origin + urlInstance.pathname\n}\n"], "mappings": "AAIO,SAAS,YAAY,KAA2B;AACrD,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO,IAAI,SAAS;AAAA,EACtB;AAEA,QAAM,cAAc,eAAe,MAAM,MAAM,IAAI,IAAI,GAAG;AAE1D,SAAO,YAAY,WAAW,SAAS,SACnC,YAAY,WACZ,YAAY,SAAS,YAAY;AACvC;", "names": []}