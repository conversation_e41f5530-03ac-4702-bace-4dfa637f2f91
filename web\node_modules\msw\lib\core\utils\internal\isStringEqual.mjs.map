{"version": 3, "sources": ["../../../../src/core/utils/internal/isStringEqual.ts"], "sourcesContent": ["/**\n * Performs a case-insensitive comparison of two given strings.\n */\nexport function isStringEqual(actual: string, expected: string): boolean {\n  return actual.toLowerCase() === expected.toLowerCase()\n}\n"], "mappings": "AAGO,SAAS,cAAc,QAAgB,UAA2B;AACvE,SAAO,OAAO,YAAY,MAAM,SAAS,YAAY;AACvD;", "names": []}